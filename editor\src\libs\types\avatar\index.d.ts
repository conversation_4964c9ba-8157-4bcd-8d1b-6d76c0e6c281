/**
 * 头像系统
 * 导出所有头像相关的组件和系统
 */
export { FacialAnimationComponent, FacialExpressionType, VisemeType } from './components/FacialAnimationComponent';
export { LipSyncComponent } from './components/LipSyncComponent';
export { AIAnimationSynthesisComponent } from './components/AIAnimationSynthesisComponent';
export { FacialAnimationEditorComponent, EditorState } from './components/FacialAnimationEditorComponent';
export { FacialAnimationSystem } from './systems/FacialAnimationSystem';
export { FacialAnimationModelAdapterSystem } from './systems/FacialAnimationModelAdapterSystem';
export { LipSyncSystem } from './systems/LipSyncSystem';
export { AIAnimationSynthesisSystem } from './systems/AIAnimationSynthesisSystem';
export { FacialAnimationEditorSystem } from './systems/FacialAnimationEditorSystem';
export { FacialAnimationModelAdapterComponent, FacialAnimationModelType } from './adapters/FacialAnimationModelAdapter';
export { FacialAnimationClip } from './animation/FacialAnimationClip';
export type { ExpressionKeyframe, VisemeKeyframe } from './animation/FacialAnimationClip';
export { EmotionBasedAnimationGenerator } from './ai/EmotionBasedAnimationGenerator';
export type { EmotionAnalysisResult } from './ai/EmotionBasedAnimationGenerator';
export { AIModel } from './ai/AIModel';
export type { AnimationGenerationRequest, AnimationGenerationResult } from './ai/AnimationGenerationTypes';
export { EmotionType } from './ai/AnimationGenerationTypes';
