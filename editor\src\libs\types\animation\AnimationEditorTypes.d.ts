/**
 * 动画编辑器专用类型定义
 * 为编辑器提供动画状态机相关的数据类型
 */

/**
 * 动画状态数据
 * 用于编辑器中的状态机状态定义
 */
export interface AnimationStateData {
  /** 状态名称 */
  name: string;
  /** 动画片段名称 */
  clip: string;
  /** 播放速度 */
  speed: number;
  /** 是否循环播放 */
  loop: boolean;
  /** 状态在编辑器中的位置 */
  position: { x: number; y: number };
}

/**
 * 参数数据
 * 用于编辑器中的状态机参数定义
 */
export interface ParameterData {
  /** 参数名称 */
  name: string;
  /** 参数类型 */
  type: 'float' | 'int' | 'bool' | 'trigger';
  /** 默认值 */
  defaultValue: any;
}

/**
 * 条件数据
 * 用于编辑器中的转换条件定义
 */
export interface ConditionData {
  /** 参数名称 */
  parameter: string;
  /** 比较操作符 */
  operator: 'equals' | 'notEquals' | 'greater' | 'less' | 'greaterEqual' | 'lessEqual';
  /** 比较值 */
  value: any;
}

/**
 * 转换规则数据
 * 用于编辑器中的状态转换定义
 */
export interface TransitionRuleData {
  /** 源状态名称 */
  from: string;
  /** 目标状态名称 */
  to: string;
  /** 转换条件列表 */
  conditions: ConditionData[];
  /** 转换持续时间（秒） */
  duration: number;
  /** 退出时间（0-1之间的比例） */
  exitTime: number;
  /** 是否有退出时间 */
  hasExitTime: boolean;
}

/**
 * 动画状态机数据
 * 用于编辑器中的完整状态机定义
 */
export interface AnimationStateMachineData {
  /** 状态机名称 */
  name: string;
  /** 状态列表 */
  states: AnimationStateData[];
  /** 转换规则列表 */
  transitions: TransitionRuleData[];
  /** 参数列表 */
  parameters: ParameterData[];
  /** 默认状态名称 */
  defaultState: string;
}

/**
 * 状态机编辑器配置
 */
export interface StateMachineEditorConfig {
  /** 网格大小 */
  gridSize: number;
  /** 是否显示网格 */
  showGrid: boolean;
  /** 状态节点大小 */
  nodeSize: { width: number; height: number };
  /** 连接线样式 */
  connectionStyle: {
    color: string;
    width: number;
    arrowSize: number;
  };
}

/**
 * 状态机编辑器事件类型
 */
export enum StateMachineEditorEventType {
  /** 状态选中 */
  STATE_SELECTED = 'stateSelected',
  /** 状态取消选中 */
  STATE_DESELECTED = 'stateDeselected',
  /** 转换选中 */
  TRANSITION_SELECTED = 'transitionSelected',
  /** 转换取消选中 */
  TRANSITION_DESELECTED = 'transitionDeselected',
  /** 状态机修改 */
  STATE_MACHINE_CHANGED = 'stateMachineChanged',
  /** 参数修改 */
  PARAMETER_CHANGED = 'parameterChanged'
}

/**
 * 状态机验证结果
 */
export interface StateMachineValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误列表 */
  errors: string[];
  /** 警告列表 */
  warnings: string[];
}

/**
 * 状态机导出选项
 */
export interface StateMachineExportOptions {
  /** 导出格式 */
  format: 'json' | 'binary';
  /** 是否压缩 */
  compress: boolean;
  /** 是否包含编辑器数据 */
  includeEditorData: boolean;
}

/**
 * 状态机导入选项
 */
export interface StateMachineImportOptions {
  /** 是否覆盖现有数据 */
  overwrite: boolean;
  /** 是否验证数据 */
  validate: boolean;
  /** 冲突解决策略 */
  conflictResolution: 'skip' | 'overwrite' | 'rename';
}
