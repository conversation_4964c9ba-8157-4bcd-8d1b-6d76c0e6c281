/**
 * 语音输入设备
 * 用于处理语音命令和语音识别
 */
import { BaseInputDevice } from '../InputDevice';
/**
 * 语音识别状态
 */
export declare enum VoiceRecognitionState {
    /** 未初始化 */
    UNINITIALIZED = "uninitialized",
    /** 初始化中 */
    INITIALIZING = "initializing",
    /** 就绪 */
    READY = "ready",
    /** 录音中 */
    RECORDING = "recording",
    /** 识别中 */
    RECOGNIZING = "recognizing",
    /** 错误 */
    ERROR = "error"
}
/**
 * 语音识别结果
 */
export interface VoiceRecognitionResult {
    /** 识别文本 */
    text: string;
    /** 置信度 */
    confidence: number;
    /** 是否为最终结果 */
    isFinal: boolean;
    /** 替代结果 */
    alternatives?: Array<{
        /** 识别文本 */
        text: string;
        /** 置信度 */
        confidence: number;
    }>;
}
/**
 * 语音命令
 */
export interface VoiceCommand {
    /** 命令名称 */
    name: string;
    /** 命令关键词 */
    keywords: string[];
    /** 命令回调函数 */
    callback: (result: VoiceRecognitionResult) => void;
    /** 置信度阈值 */
    confidenceThreshold?: number;
}
/**
 * 语音输入设备选项
 */
export interface VoiceDeviceOptions {
    /** 是否自动开始 */
    autoStart?: boolean;
    /** 语言 */
    language?: string;
    /** 是否连续识别 */
    continuous?: boolean;
    /** 是否返回中间结果 */
    interimResults?: boolean;
    /** 最大替代结果数 */
    maxAlternatives?: number;
    /** 置信度阈值 */
    confidenceThreshold?: number;
    /** 语音命令列表 */
    commands?: VoiceCommand[];
}
/**
 * 语音输入设备
 */
export declare class VoiceDevice extends BaseInputDevice {
    /** 语音识别器 */
    private recognition;
    /** 语音合成器 */
    private synthesis;
    /** 语音识别状态 */
    private state;
    /** 是否自动开始 */
    private autoStart;
    /** 语言 */
    private language;
    /** 是否连续识别 */
    private continuous;
    /** 是否返回中间结果 */
    private interimResults;
    /** 最大替代结果数 */
    private maxAlternatives;
    /** 置信度阈值 */
    private confidenceThreshold;
    /** 语音命令列表 */
    private commands;
    /** 当前识别结果 */
    private currentResult;
    /** 是否支持语音识别 */
    private isRecognitionSupported;
    /** 是否支持语音合成 */
    private isSynthesisSupported;
    /**
     * 创建语音输入设备
     * @param options 选项
     */
    constructor(options?: VoiceDeviceOptions);
    /**
     * 检查浏览器支持
     */
    private checkBrowserSupport;
    /**
     * 初始化设备
     */
    initialize(): void;
    /**
     * 初始化语音识别
     */
    private initSpeechRecognition;
    /**
     * 初始化语音合成
     */
    private initSpeechSynthesis;
    /**
     * 处理识别开始事件
     */
    private handleRecognitionStart;
    /**
     * 处理识别结果事件
     * @param event 识别结果事件
     */
    private handleRecognitionResult;
    /**
     * 处理识别错误事件
     * @param event 错误事件
     */
    private handleRecognitionError;
    /**
     * 处理识别结束事件
     */
    private handleRecognitionEnd;
    /**
     * 处理语音命令
     * @param result 识别结果
     */
    private processCommands;
    /**
     * 开始语音识别
     */
    startRecognition(): void;
    /**
     * 停止语音识别
     */
    stopRecognition(): void;
    /**
     * 添加语音命令
     * @param commands 语音命令列表
     */
    addCommands(commands: VoiceCommand[]): void;
    /**
     * 移除语音命令
     * @param name 命令名称
     */
    removeCommand(name: string): void;
    /**
     * 清除所有语音命令
     */
    clearCommands(): void;
    /**
     * 获取当前识别结果
     * @returns 识别结果
     */
    getResult(): VoiceRecognitionResult | null;
    /**
     * 获取当前识别状态
     * @returns 识别状态
     */
    getState(): VoiceRecognitionState;
    /**
     * 语音合成
     * @param text 文本
     * @param options 选项
     */
    speak(text: string, options?: SpeechSynthesisUtteranceOptions): void;
    /**
     * 销毁设备
     */
    destroy(): void;
}
