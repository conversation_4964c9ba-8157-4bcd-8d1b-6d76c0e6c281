/**
 * 视口组件
 */
import React, { useRef, useEffect } from 'react';
import { Button, Tooltip, Radio } from 'antd';
import {
  BorderOutlined,
  FullscreenOutlined,
  CameraOutlined,
  AimOutlined,
  DragOutlined,
  ScissorOutlined,
  RotateLeftOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { setViewportMode, setRenderMode, ViewportMode, RenderMode } from '../../store/ui/uiSlice';
import { Engine } from '../../libs/types/core';
import { Scene } from '../../libs/types/scene';
import { Camera, CameraType } from '../../libs/types/rendering';
import './Viewport.less';

const Viewport: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { viewportMode, renderMode } = useSelector((state: RootState) => state.ui);
  const { entities, selectedEntityId } = useSelector((state: RootState) => state.scene);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // 初始化引擎
  useEffect(() => {
    if (!canvasRef.current || !containerRef.current) return;

    // 创建引擎实例
    const engine = new Engine({
      canvas: canvasRef.current,
      autoStart: false,
      debug: false
    });

    // 创建场景
    const scene = new Scene('MainScene');
    console.log('Scene created:', scene);

    // 创建相机
    const camera = new Camera(CameraType.PERSPECTIVE);
    console.log('Camera created:', camera);

    // 初始化引擎
    engine.initialize();

    // 启动引擎
    engine.start();

    // 处理窗口大小变化
    const handleResize = () => {
      if (!containerRef.current) return;

      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight;

      // 设置渲染器大小
      const renderer = engine.getRenderer();
      if (renderer) {
        renderer.setSize(width, height);
      }
    };

    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
      engine.stop();
      engine.dispose();
    };
  }, []);
  
  // 更新场景实体
  useEffect(() => {
    // 这里应该将Redux中的实体数据同步到引擎场景中
    // 由于我们没有实际的引擎实现，这里只是一个示例
    console.log('Scene entities updated:', entities);
  }, [entities]);
  
  // 处理视口模式变化
  const handleViewportModeChange = (mode: string) => {
    dispatch(setViewportMode(mode as ViewportMode));
  };
  
  // 处理渲染模式变化
  const handleRenderModeChange = (e: any) => {
    dispatch(setRenderMode(e.target.value as RenderMode));
  };
  
  return (
    <div className="viewport-container" ref={containerRef}>
      <canvas ref={canvasRef} className="viewport-canvas" />
      
      <div className="viewport-toolbar">
        <div className="toolbar-group">
          <Tooltip title={t('editor.viewport.select')}>
            <Button
              type={viewportMode === 'select' ? 'primary' : 'default'}
              icon={<BorderOutlined />}
              onClick={() => handleViewportModeChange('select')}
            />
          </Tooltip>
          <Tooltip title={t('editor.viewport.translate')}>
            <Button
              type={viewportMode === 'translate' ? 'primary' : 'default'}
              icon={<DragOutlined />}
              onClick={() => handleViewportModeChange('translate')}
            />
          </Tooltip>
          <Tooltip title={t('editor.viewport.rotate')}>
            <Button
              type={viewportMode === 'rotate' ? 'primary' : 'default'}
              icon={<RotateLeftOutlined />}
              onClick={() => handleViewportModeChange('rotate')}
            />
          </Tooltip>
          <Tooltip title={t('editor.viewport.scale')}>
            <Button
              type={viewportMode === 'scale' ? 'primary' : 'default'}
              icon={<ScissorOutlined />}
              onClick={() => handleViewportModeChange('scale')}
            />
          </Tooltip>
        </div>
        
        <div className="toolbar-group">
          <Tooltip title={t('editor.viewport.focus')}>
            <Button
              icon={<AimOutlined />}
              disabled={!selectedEntityId}
            />
          </Tooltip>
          <Tooltip title={t('editor.viewport.screenshot')}>
            <Button icon={<CameraOutlined />} />
          </Tooltip>
          <Tooltip title={t('editor.viewport.fullscreen')}>
            <Button icon={<FullscreenOutlined />} />
          </Tooltip>
        </div>
      </div>
      
      <div className="viewport-status">
        <Radio.Group value={renderMode} onChange={handleRenderModeChange} size="small">
          <Radio.Button value="solid">{t('editor.viewport.renderModes.solid')}</Radio.Button>
          <Radio.Button value="wireframe">{t('editor.viewport.renderModes.wireframe')}</Radio.Button>
          <Radio.Button value="textured">{t('editor.viewport.renderModes.textured')}</Radio.Button>
        </Radio.Group>
        
        <div className="status-info">
          {selectedEntityId ? (
            <span>
              {entities.find(e => e.id === selectedEntityId)?.name || 'Unknown'} 
              ({t('editor.viewport.selected')})
            </span>
          ) : (
            <span>{t('editor.viewport.noSelection')}</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default Viewport;
