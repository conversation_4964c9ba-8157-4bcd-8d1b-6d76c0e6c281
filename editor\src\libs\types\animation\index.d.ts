/**
 * 动画系统模块
 * 导出所有动画系统相关的类和接口
 */
export * from './AnimationClip';
export * from './Animator';
export * from './BlendSpace1D';
export { BlendSpace2D } from './BlendSpace2D';
export type { BlendSpace2DConfig } from './BlendSpace2D';
export { AnimationStateMachine } from './AnimationStateMachine';
export type { TransitionRule, AnimationStateMachineEventType, ParameterMetadata } from './AnimationStateMachine';
export * from './AnimationRetargeting';
export { AnimationRetargeter } from './AnimationRetargeter';
export type { RetargetConfig, RetargetEventType } from './AnimationRetargeter';
export * from './RetargetingSystem';
export * from './SkeletonAnimation';
export { AnimationEventComponent, AnimationEventSystem, AnimationEventType as AnimationEventTypeFromEvent } from './AnimationEvent';
export * from './AnimationInstancing';
export * from './GPUSkinning';
export * from './FacialAnimation';
export { FacialAnimationSystem } from './FacialAnimationSystem';
export * from './LipSync';
export * from './FacialAnimationEditor';
export { FacialAnimationEditorSystem } from './FacialAnimationEditorSystem';
export * from './adapters';
export * from './AIAnimationSynthesis';
export { AIAnimationSynthesisSystem } from './AIAnimationSynthesisSystem';
export { PhysicsBasedAnimationComponent, PhysicsBasedAnimationSystem } from './PhysicsBasedAnimation';
export type { PhysicsConstraintConfig as PhysicsConstraintConfigFromAnimation } from './PhysicsBasedAnimation';
export * from './physics';
export * from '../avatar/components/AvatarAnimationComponent';
export * from '../avatar/components/AvatarRigComponent';
export * from '../avatar/systems/AvatarAnimationSystem';
export * from './AnimationEditorTypes';
