/**
 * AI模型管理器
 * 负责加载、管理和使用AI模型
 */
import { System } from '../core/System';
import type { World } from '../core/World';
import { AIModelType } from './AIModelType';
import { AIModelConfig } from './AIModelConfig';
import { AIModelLoadOptions } from './AIModelLoadOptions';
import { IAIModel } from './models/IAIModel';
/**
 * AI模型管理器配置
 */
export interface AIModelManagerConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 模型缓存大小 */
    cacheSize?: number;
    /** 是否使用本地模型 */
    useLocalModels?: boolean;
    /** 模型API密钥 */
    apiKeys?: Record<string, string>;
    /** 模型基础URL */
    baseUrls?: Record<string, string>;
    /** 模型版本 */
    modelVersions?: Record<string, string>;
}
/**
 * AI模型管理器
 * 负责加载、管理和使用AI模型
 */
export declare class AIModelManager extends System {
    /** 系统优先级 */
    static readonly PRIORITY = 50;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 已加载的模型 */
    private loadedModels;
    /** 模型加载进度 */
    private modelLoadProgress;
    /** 事件发射器 */
    private eventEmitter;
    /** 模型工厂 */
    private modelFactory;
    /** 模型加载队列 */
    private loadQueue;
    /** 是否正在加载模型 */
    private isLoading;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: AIModelManagerConfig);
    /**
     * 初始化
     */
    initialize(): void;
    /**
     * 加载模型
     * @param modelType 模型类型
     * @param config 模型配置
     * @param options 加载选项
     * @returns 模型实例
     */
    loadModel(modelType: AIModelType, config?: AIModelConfig, options?: AIModelLoadOptions): Promise<IAIModel | null>;
    /**
     * 处理加载队列
     */
    private processLoadQueue;
    /**
     * 创建模型实例
     * @param modelType 模型类型
     * @param config 模型配置
     * @param options 加载选项
     * @returns 模型实例
     */
    private createModelInstance;
    /**
     * 生成模型ID
     * @param modelType 模型类型
     * @param config 模型配置
     * @returns 模型ID
     */
    private generateModelId;
    /**
     * 获取已加载的模型
     * @param modelId 模型ID
     * @returns 模型实例
     */
    getModel(modelId: string): IAIModel | null;
    /**
     * 获取所有已加载的模型
     * @returns 模型实例映射
     */
    getAllModels(): Map<string, IAIModel>;
    /**
     * 卸载模型
     * @param modelId 模型ID
     * @returns 是否成功
     */
    unloadModel(modelId: string): boolean;
    /**
     * 卸载所有模型
     */
    unloadAllModels(): void;
    /**
     * 获取模型加载进度
     * @param modelId 模型ID
     * @returns 加载进度 (0-1)
     */
    getModelLoadProgress(modelId: string): number;
    /**
     * 监听事件
     * @param event 事件名称
     * @param listener 监听器
     * @returns this 实例，用于链式调用
     */
    addListener(event: string, listener: (...args: any[]) => void): this;
    /**
     * 取消监听事件
     * @param event 事件名称
     * @param listener 监听器
     * @returns this 实例，用于链式调用
     */
    removeListener(event: string, listener: (...args: any[]) => void): this;
    /**
     * 监听事件（兼容System类）
     * @param event 事件名称
     * @param callback 回调函数
     * @returns this 实例，用于链式调用
     */
    on(event: string, callback: (...args: any[]) => void): this;
    /**
     * 取消监听事件（兼容System类）
     * @param event 事件名称
     * @param callback 回调函数
     * @returns this 实例，用于链式调用
     */
    off(event: string, callback?: (...args: any[]) => void): this;
    /**
     * 销毁
     */
    dispose(): void;
}
